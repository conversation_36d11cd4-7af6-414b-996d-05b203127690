#!/usr/bin/env python3
# Copyright (c) 2025 PaddlePaddle Authors. All Rights Reserved.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

"""
Test build script for MOE cc>=70 compatibility.
This script tests the compilation of the compatibility layer.
"""

import os
import sys
import subprocess
import paddle
from paddle.utils.cpp_extension import CUDAExtension, setup

def get_compute_capability():
    """Get CUDA compute capability."""
    try:
        if paddle.is_compiled_with_cuda():
            device_id = paddle.get_device().split(':')[-1]
            prop = paddle.device.cuda.get_device_properties(int(device_id))
            return prop.major * 10 + prop.minor
    except:
        pass
    return 70  # Default

def test_compile_compatibility():
    """Test compilation of compatibility files."""
    print("Testing MOE cc>=70 Compatibility Compilation")
    print("=" * 50)
    
    cc = get_compute_capability()
    print(f"Detected compute capability: {cc}")
    
    # Basic compilation flags
    nvcc_compile_args = [
        "-DPADDLE_DEV",
        "-DPADDLE_ON_INFERENCE", 
        "-DPy_LIMITED_API=0x03090000",
        f"-gencode=arch=compute_{cc},code=sm_{cc}",
    ]
    
    if cc >= 70:
        nvcc_compile_args += [
            "-DMOE_COMPATIBILITY_CC70=1",
            "-DMOE_COMPATIBILITY_NO_BF16=1", 
            "-DMOE_COMPATIBILITY_USE_FP16=1",
            "-DMOE_COMPATIBILITY_USE_SIMT=1",
        ]
    
    # Test sources - only include files that exist
    test_sources = []
    
    potential_sources = [
        "custom_ops/gpu_ops/moe/moe_gemm_cc70_compat.cu",
        "custom_ops/gpu_ops/cutlass_kernels/moe_gemm/fused_moe_gemm_kernels_cc70_compat.cu",
    ]
    
    for source in potential_sources:
        if os.path.exists(source):
            test_sources.append(source)
            print(f"✅ Found source: {source}")
        else:
            print(f"⚠️  Missing source: {source}")
    
    if not test_sources:
        print("❌ No test sources found")
        return False
    
    # Test include directories
    include_dirs = [
        "custom_ops/gpu_ops",
        "custom_ops/gpu_ops/moe",
        "custom_ops/gpu_ops/cutlass_kernels",
    ]
    
    existing_includes = []
    for inc_dir in include_dirs:
        if os.path.exists(inc_dir):
            existing_includes.append(inc_dir)
            print(f"✅ Found include dir: {inc_dir}")
        else:
            print(f"⚠️  Missing include dir: {inc_dir}")
    
    try:
        # Test compilation
        print("\n🔨 Testing compilation...")
        
        extension = CUDAExtension(
            sources=test_sources,
            extra_compile_args={
                "nvcc": nvcc_compile_args
            },
            include_dirs=existing_includes,
            libraries=["cublasLt"],
        )
        
        # Try to build (this will test compilation without actually installing)
        setup(
            name="moe_cc70_test",
            ext_modules=extension,
            script_args=['build_ext', '--inplace', '--force']
        )
        
        print("✅ Compilation test passed!")
        return True
        
    except Exception as e:
        print(f"❌ Compilation test failed: {e}")
        return False

def test_python_imports():
    """Test Python compatibility imports."""
    print("\n🐍 Testing Python imports...")
    
    try:
        # Test basic imports
        from fastdeploy.model_executor.layers.moe.fused_moe_cc70_backend import CC70CompatMoEMethod
        print("✅ Successfully imported CC70CompatMoEMethod")
        
        # Test method instantiation
        method = CC70CompatMoEMethod(None)
        print("✅ Successfully instantiated CC70CompatMoEMethod")
        
        # Test compute capability detection
        cc = method._get_compute_capability()
        print(f"✅ Detected compute capability: {cc}")
        
        return True
        
    except Exception as e:
        print(f"❌ Python import test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_header_syntax():
    """Test header file syntax."""
    print("\n📄 Testing header file syntax...")
    
    headers = [
        "custom_ops/gpu_ops/moe/moe_compatibility.h",
        "custom_ops/gpu_ops/cutlass_kernels/moe_gemm/fused_moe_gemm_kernels_cc70_compat.h",
    ]
    
    for header in headers:
        if os.path.exists(header):
            try:
                # Simple syntax check by trying to compile a test file
                test_cpp = f"""
#include "{header}"
int main() {{ return 0; }}
"""
                with open("test_header.cpp", "w") as f:
                    f.write(test_cpp)
                
                # Try to compile with nvcc
                result = subprocess.run([
                    "nvcc", "-c", "test_header.cpp", "-o", "test_header.o",
                    "-std=c++17", "-DMOE_COMPATIBILITY_CC70=1"
                ], capture_output=True, text=True)
                
                if result.returncode == 0:
                    print(f"✅ Header syntax OK: {header}")
                else:
                    print(f"❌ Header syntax error: {header}")
                    print(f"   Error: {result.stderr}")
                
                # Cleanup
                for temp_file in ["test_header.cpp", "test_header.o"]:
                    if os.path.exists(temp_file):
                        os.remove(temp_file)
                        
            except Exception as e:
                print(f"⚠️  Could not test header {header}: {e}")
        else:
            print(f"⚠️  Header not found: {header}")
    
    return True

def main():
    """Run all tests."""
    print("MOE cc>=70 Compatibility Build Test")
    print("=" * 50)
    
    if not paddle.is_compiled_with_cuda():
        print("❌ CUDA not available, skipping tests")
        return False
    
    tests = [
        ("Header Syntax", test_header_syntax),
        ("Python Imports", test_python_imports),
        ("Compilation", test_compile_compatibility),
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            print(f"\n🧪 Running {test_name} test...")
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ Test '{test_name}' crashed: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 50)
    print("Test Summary:")
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"  {status}: {test_name}")
        if result:
            passed += 1
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Ready to build MOE cc>=70 compatibility.")
        return True
    else:
        print("⚠️  Some tests failed. Check the implementation.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
