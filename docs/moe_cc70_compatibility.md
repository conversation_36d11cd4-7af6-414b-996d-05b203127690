# MOE cc>=70 Compatibility Implementation

## Overview

This document describes the compatibility implementation for Mixture of Experts (MOE) operations on CUDA compute capability >= 70 devices. The original MOE implementation required cc>=80 due to its reliance on Tensor Core operations and BF16 precision. This compatibility layer enables MOE operations on older GPU architectures with appropriate fallbacks.

## Supported Architectures

| Compute Capability | GPU Examples | MOE Support Level |
|-------------------|--------------|-------------------|
| cc >= 80 | A100, H100, RTX 30/40 series | Full support (original implementation) |
| cc >= 75 | RTX 20 series, T4 | Compatibility mode with Tensor Core |
| cc >= 70 | V100, RTX 10 series | Compatibility mode with SIMT fallback |
| cc < 70 | GTX 10 series and older | Limited compatibility mode |

## Key Compatibility Features

### 1. Precision Fallbacks

- **BF16 → FP16**: For cc < 80, BF16 operations are automatically converted to FP16
- **Mixed Precision**: Maintains computational accuracy while ensuring compatibility
- **Automatic Detection**: Runtime detection of precision support

### 2. Compute Mode Fallbacks

- **Tensor Core → SIMT**: For cc < 75, falls back to SIMT operations
- **Optimized Kernels**: Custom SIMT kernels optimized for cc70 architectures
- **Memory Optimization**: Reduced shared memory usage for older architectures

### 3. Quantization Support

| Quantization | cc >= 80 | cc >= 75 | cc >= 70 | cc < 70 |
|-------------|----------|----------|----------|---------|
| BF16 | ✅ | ❌ | ❌ | ❌ |
| FP16 | ✅ | ✅ | ✅ | ✅ |
| INT8 | ✅ | ✅ | ✅ | ❌ |
| INT4 | ✅ | ✅ | ❌ | ❌ |
| FP8 | ✅ (cc>=89) | ❌ | ❌ | ❌ |

## Implementation Details

### File Structure

```
custom_ops/gpu_ops/moe/
├── moe_compatibility.h                    # Compatibility macros and definitions
├── moe_gemm_cc70_compat.cu              # SIMT-based GEMM kernels for cc70
└── fused_moe.cu                          # Modified to include compatibility

custom_ops/gpu_ops/cutlass_kernels/moe_gemm/
├── fused_moe_gemm_kernels_cc70_compat.h  # CUTLASS compatibility templates
└── fused_moe_gemm_kernels_cc70_compat.cu # CUTLASS kernel implementations

fastdeploy/model_executor/layers/moe/
├── fused_moe_cc70_backend.py             # Python compatibility backend
└── moe.py                                # Modified for automatic backend selection
```

### Compilation Flags

The compatibility implementation uses several compilation flags:

```cpp
// Automatically set based on compute capability
#define MOE_COMPATIBILITY_CC70          // Enable cc70 compatibility mode
#define MOE_COMPATIBILITY_NO_BF16       // Disable BF16, use FP16 instead
#define MOE_COMPATIBILITY_USE_FP16      // Force FP16 precision
#define MOE_COMPATIBILITY_USE_SIMT      // Use SIMT instead of Tensor Core
```

### Kernel Optimizations

#### SIMT GEMM Kernel
- **Tile Size**: Optimized 16x16 tiles for cc70
- **Shared Memory**: Conservative 32KB usage
- **Thread Block**: 256 threads per block
- **Memory Coalescing**: 128-byte aligned accesses

#### Quantized GEMM Kernel
- **INT8 Support**: Full INT8 weight quantization
- **Scale Handling**: Per-channel scaling factors
- **Accumulation**: INT32 accumulation for INT8 operations

## Usage

### Automatic Backend Selection

The MOE implementation automatically selects the appropriate backend based on the detected compute capability:

```python
from fastdeploy.model_executor.layers.moe.moe import get_moe_method

# Automatically selects the best backend for current device
moe_method = get_moe_method()
```

### Manual Backend Selection

For testing or specific use cases, you can manually select the compatibility backend:

```python
from fastdeploy.model_executor.layers.moe.fused_moe_cc70_backend import CC70CompatMoEMethod

# Force cc70 compatibility mode
moe_method = CC70CompatMoEMethod(quant_config=None)
```

### Environment Variables

Control compatibility behavior with environment variables:

```bash
# Force cc70 compatibility mode
export MOE_FORCE_CC70_COMPAT=1

# Disable BF16 even on supported hardware
export MOE_DISABLE_BF16=1

# Force SIMT mode instead of Tensor Core
export MOE_FORCE_SIMT=1
```

## Performance Considerations

### Expected Performance Impact

| Architecture | Performance vs cc>=80 | Notes |
|-------------|----------------------|-------|
| cc >= 80 | 100% (baseline) | Full Tensor Core + BF16 |
| cc >= 75 | ~80-90% | Tensor Core + FP16 |
| cc >= 70 | ~60-75% | SIMT + FP16 |
| cc < 70 | ~40-60% | Limited SIMT + FP16 |

### Optimization Tips

1. **Batch Size**: Use larger batch sizes to amortize kernel launch overhead
2. **Expert Count**: Limit to 64 experts for cc70 to reduce memory pressure
3. **Sequence Length**: Prefer longer sequences for better SIMT utilization
4. **Memory**: Monitor GPU memory usage as cc70 has less efficient memory patterns

## Testing

Run the compatibility test suite:

```bash
python test_moe_cc70_compatibility.py
```

The test suite verifies:
- ✅ Compute capability detection
- ✅ Backend selection logic
- ✅ Precision fallback mechanisms
- ✅ Basic MOE operations
- ✅ Quantization support levels

## Troubleshooting

### Common Issues

1. **Compilation Errors**
   ```bash
   # Ensure CUDA toolkit version compatibility
   nvcc --version
   # Should be >= 10.2 for cc70 support
   ```

2. **Runtime Errors**
   ```python
   # Check compute capability
   import paddle
   prop = paddle.device.cuda.get_device_properties(0)
   print(f"Compute capability: {prop.major}.{prop.minor}")
   ```

3. **Performance Issues**
   ```python
   # Enable profiling to identify bottlenecks
   import paddle.profiler as profiler
   with profiler.Profiler() as prof:
       # Your MOE code here
       pass
   ```

### Debug Mode

Enable debug logging:

```python
import os
os.environ['MOE_DEBUG'] = '1'

# Will print compatibility decisions and kernel selections
```

## Limitations

### Current Limitations

1. **INT4 Quantization**: Not supported on cc70 due to hardware limitations
2. **FP8 Quantization**: Requires cc>=89, not available in compatibility mode
3. **Grouped GEMM**: Sequential execution instead of true grouped GEMM on cc70
4. **Memory Bandwidth**: Lower effective bandwidth due to SIMT vs Tensor Core

### Future Improvements

1. **Optimized SIMT Kernels**: Further optimization of cc70 SIMT kernels
2. **Memory Layout**: Improved memory layouts for cc70 architectures
3. **Kernel Fusion**: More aggressive kernel fusion for cc70
4. **Auto-tuning**: Automatic kernel parameter tuning based on hardware

## Contributing

When contributing to the compatibility implementation:

1. **Test on Multiple Architectures**: Verify on cc70, cc75, and cc80+ devices
2. **Maintain Backward Compatibility**: Ensure changes don't break existing functionality
3. **Performance Benchmarking**: Include performance comparisons in PRs
4. **Documentation**: Update this document for any new compatibility features

## References

- [CUDA Compute Capability](https://developer.nvidia.com/cuda-gpus)
- [CUTLASS Documentation](https://github.com/NVIDIA/cutlass)
- [Tensor Core Programming Guide](https://docs.nvidia.com/cuda/cuda-c-programming-guide/index.html#tensor-cores)
