#!/usr/bin/env python3
# Copyright (c) 2025 PaddlePaddle Authors. All Rights Reserved.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

"""
Test script to verify MOE build fixes.
This script tests the compilation after fixing missing MOE source files.
"""

import os
import sys
import subprocess
import paddle

def check_moe_source_files():
    """Check if all required MOE source files exist."""
    print("🔍 Checking MOE source files...")
    
    required_files = [
        "custom_ops/gpu_ops/moe/moe_deepgemm_permute.cu",
        "custom_ops/gpu_ops/moe/moe_deepgemm_depermute.cu",
        "custom_ops/gpu_ops/moe/fused_moe.cu",
        "custom_ops/gpu_ops/moe/moe_dispatch.cu",
        "custom_ops/gpu_ops/moe/moe_reduce.cu",
        "custom_ops/gpu_ops/moe/tritonmoe_preprocess.cu",
        "custom_ops/gpu_ops/moe/moe_simple_cc70.cu",
        "custom_ops/gpu_ops/moe/moe_compatibility.h",
    ]
    
    missing_files = []
    for file_path in required_files:
        if os.path.exists(file_path):
            print(f"✅ Found: {file_path}")
        else:
            print(f"❌ Missing: {file_path}")
            missing_files.append(file_path)
    
    if missing_files:
        print(f"\n❌ Missing {len(missing_files)} required files")
        return False
    else:
        print(f"\n✅ All {len(required_files)} required files found")
        return True

def check_compute_capability():
    """Check CUDA compute capability."""
    print("\n🖥️  Checking compute capability...")
    
    if not paddle.is_compiled_with_cuda():
        print("❌ CUDA not available")
        return None
    
    try:
        device_id = paddle.get_device().split(':')[-1]
        prop = paddle.device.cuda.get_device_properties(int(device_id))
        cc = prop.major * 10 + prop.minor
        print(f"✅ Detected compute capability: {cc}")
        print(f"   GPU: {prop.name}")
        print(f"   Memory: {prop.total_memory // (1024**3)} GB")
        return cc
    except Exception as e:
        print(f"❌ Failed to get compute capability: {e}")
        return None

def test_setup_ops_syntax():
    """Test setup_ops.py syntax."""
    print("\n📝 Testing setup_ops.py syntax...")
    
    try:
        # Try to import and parse setup_ops.py
        result = subprocess.run([
            sys.executable, "-m", "py_compile", "custom_ops/setup_ops.py"
        ], capture_output=True, text=True, cwd=".")
        
        if result.returncode == 0:
            print("✅ setup_ops.py syntax is valid")
            return True
        else:
            print("❌ setup_ops.py syntax error:")
            print(result.stderr)
            return False
    except Exception as e:
        print(f"❌ Failed to test setup_ops.py: {e}")
        return False

def test_dry_run_build():
    """Test a dry run of the build process."""
    print("\n🔨 Testing dry run build...")
    
    try:
        # Change to custom_ops directory
        os.chdir("custom_ops")
        
        # Run setup with --help to test basic parsing
        result = subprocess.run([
            sys.executable, "setup_ops.py", "--help"
        ], capture_output=True, text=True, timeout=30)
        
        if result.returncode == 0:
            print("✅ setup_ops.py can be executed")
            return True
        else:
            print("❌ setup_ops.py execution failed:")
            print("STDOUT:", result.stdout)
            print("STDERR:", result.stderr)
            return False
            
    except subprocess.TimeoutExpired:
        print("⚠️  setup_ops.py --help timed out (this might be normal)")
        return True
    except Exception as e:
        print(f"❌ Dry run build failed: {e}")
        return False
    finally:
        # Change back to original directory
        os.chdir("..")

def test_moe_function_declarations():
    """Test if MOE function declarations are consistent."""
    print("\n🔍 Checking MOE function declarations...")
    
    try:
        # Check if cpp_extensions.cc has the required declarations
        cpp_ext_path = "custom_ops/gpu_ops/cpp_extensions.cc"
        if not os.path.exists(cpp_ext_path):
            print(f"❌ {cpp_ext_path} not found")
            return False
        
        with open(cpp_ext_path, 'r') as f:
            content = f.read()
        
        required_functions = [
            "MoEDeepGEMMPermute",
            "MoEDeepGEMMDePermute",
        ]
        
        missing_functions = []
        for func in required_functions:
            if func in content:
                print(f"✅ Found declaration: {func}")
            else:
                print(f"❌ Missing declaration: {func}")
                missing_functions.append(func)
        
        if missing_functions:
            print(f"\n❌ Missing {len(missing_functions)} function declarations")
            return False
        else:
            print(f"\n✅ All {len(required_functions)} function declarations found")
            return True
            
    except Exception as e:
        print(f"❌ Failed to check function declarations: {e}")
        return False

def main():
    """Run all tests."""
    print("MOE Build Fix Verification")
    print("=" * 40)
    
    tests = [
        ("Source Files", check_moe_source_files),
        ("Compute Capability", check_compute_capability),
        ("setup_ops.py Syntax", test_setup_ops_syntax),
        ("Function Declarations", test_moe_function_declarations),
        ("Dry Run Build", test_dry_run_build),
    ]
    
    results = []
    cc = None
    
    for test_name, test_func in tests:
        try:
            print(f"\n🧪 Running {test_name} test...")
            result = test_func()
            if test_name == "Compute Capability":
                cc = result
                result = cc is not None
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ Test '{test_name}' crashed: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 40)
    print("Test Summary:")
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"  {status}: {test_name}")
        if result:
            passed += 1
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        print("\n🎉 All tests passed! Ready to build MOE with fixes.")
        print("\n💡 Next steps:")
        print("   1. cd custom_ops")
        print("   2. python setup_ops.py build_ext --inplace")
        if cc:
            if cc >= 80:
                print(f"   3. Your GPU (cc{cc}) supports full MOE features")
            elif cc >= 70:
                print(f"   3. Your GPU (cc{cc}) will use compatibility mode")
            else:
                print(f"   3. Your GPU (cc{cc}) has limited MOE support")
        return True
    else:
        print("\n⚠️  Some tests failed. Please fix the issues before building.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
