// Copyright (c) 2025 PaddlePaddle Authors. All Rights Reserved.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

#include "fused_moe_gemm_kernels_cc70_compat.h"
#include "fused_moe_gemm_kernels.h"
#include "../moe/moe_compatibility.h"

#ifdef MOE_COMPATIBILITY_CC70

// Explicit template instantiations for cc70 compatibility

namespace cutlass {
namespace gemm {
namespace kernel {

// FP16 MOE GEMM runner for cc70
template class MoeGemmRunner<cutlass::half_t, WeightOnlyQuantTraits<cutlass::half_t, cutlass::arch::Sm70>>;

// INT8 quantized MOE GEMM runner for cc70  
template class MoeGemmRunner<cutlass::half_t, WeightOnlyQuantTraits<int8_t, cutlass::arch::Sm70>>;

} // namespace kernel
} // namespace gemm  
} // namespace cutlass

// C++ API functions for cc70 compatibility

extern "C" {

// FP16 MOE GEMM for cc70
cudaError_t moe_gemm_fp16_cc70(
    const void* A,
    const void* B, 
    void* C,
    const void* bias,
    const int64_t* total_rows_before_expert,
    int64_t total_rows,
    int64_t gemm_n,
    int64_t gemm_k,
    int num_experts,
    cudaStream_t stream) {
    
    using T = cutlass::half_t;
    using WeightTraits = WeightOnlyQuantTraits<T, cutlass::arch::Sm70>;
    
    static MoeGemmRunner<T, WeightTraits> runner;
    
    try {
        runner.moe_gemm(
            static_cast<const T*>(A),
            static_cast<const T*>(B),
            nullptr, // weight_scales (not used for FP16)
            static_cast<T*>(C),
            const_cast<int64_t*>(total_rows_before_expert),
            total_rows,
            total_rows, // tune_total_rows
            gemm_n,
            gemm_k,
            num_experts,
            typename WeightTraits::Arguments{}, // empty args for FP16
            stream
        );
        return cudaSuccess;
    } catch (...) {
        return cudaErrorLaunchFailure;
    }
}

// INT8 quantized MOE GEMM for cc70
cudaError_t moe_gemm_int8_cc70(
    const void* A,
    const void* B_quant,
    const void* B_scales,
    void* C,
    const void* bias,
    const int64_t* total_rows_before_expert,
    int64_t total_rows,
    int64_t gemm_n,
    int64_t gemm_k,
    int num_experts,
    cudaStream_t stream) {
    
    using T = cutlass::half_t;
    using WeightTraits = WeightOnlyQuantTraits<int8_t, cutlass::arch::Sm70>;
    
    static MoeGemmRunner<T, WeightTraits> runner;
    
    try {
        runner.moe_gemm(
            static_cast<const T*>(A),
            static_cast<const int8_t*>(B_quant),
            static_cast<const T*>(B_scales),
            static_cast<T*>(C),
            const_cast<int64_t*>(total_rows_before_expert),
            total_rows,
            total_rows, // tune_total_rows
            gemm_n,
            gemm_k,
            num_experts,
            typename WeightTraits::Arguments{}, // quantization args
            stream
        );
        return cudaSuccess;
    } catch (...) {
        return cudaErrorLaunchFailure;
    }
}

// MOE GEMM with bias and activation for cc70
cudaError_t moe_gemm_bias_act_cc70(
    const void* A,
    const void* B,
    const void* weight_scales,
    const void* biases,
    void* C,
    const int64_t* total_rows_before_expert,
    int64_t total_rows,
    int64_t gemm_n,
    int64_t gemm_k,
    int num_experts,
    const char* activation_type,
    cudaStream_t stream) {
    
    using T = cutlass::half_t;
    using WeightTraits = WeightOnlyQuantTraits<T, cutlass::arch::Sm70>;
    
    static MoeGemmRunner<T, WeightTraits> runner;
    
    try {
        std::string act_type(activation_type);
        runner.moe_gemm_bias_act(
            static_cast<const T*>(A),
            static_cast<const T*>(B),
            static_cast<const T*>(weight_scales),
            static_cast<const T*>(biases),
            static_cast<T*>(C),
            const_cast<int64_t*>(total_rows_before_expert),
            total_rows,
            total_rows, // tune_total_rows
            gemm_n,
            gemm_k,
            num_experts,
            typename WeightTraits::Arguments{},
            act_type,
            stream
        );
        return cudaSuccess;
    } catch (...) {
        return cudaErrorLaunchFailure;
    }
}

// Check if current device supports cc70 compatibility mode
bool is_cc70_compatible_device() {
    int device;
    if (cudaGetDevice(&device) != cudaSuccess) {
        return false;
    }
    
    cudaDeviceProp prop;
    if (cudaGetDeviceProperties(&prop, device) != cudaSuccess) {
        return false;
    }
    
    int cc = prop.major * 10 + prop.minor;
    return cc >= 70;
}

// Get recommended configuration for cc70 device
void get_cc70_moe_config(
    int* max_experts,
    int* max_tokens_per_expert,
    int* preferred_block_size,
    size_t* max_shared_memory) {
    
    if (max_experts) *max_experts = 64;  // Conservative limit for cc70
    if (max_tokens_per_expert) *max_tokens_per_expert = 1024;
    if (preferred_block_size) *preferred_block_size = MOE_PREFERRED_BLOCK_SIZE;
    if (max_shared_memory) *max_shared_memory = MOE_SHARED_MEMORY_SIZE;
}

} // extern "C"

// Paddle custom operator registration for cc70 compatibility

#include "paddle/extension.h"

std::vector<paddle::Tensor> moe_gemm_cc70_op(
    const paddle::Tensor& A,
    const paddle::Tensor& B,
    const paddle::optional<paddle::Tensor>& weight_scales,
    const paddle::optional<paddle::Tensor>& bias,
    const paddle::Tensor& total_rows_before_expert,
    int64_t total_rows,
    int64_t gemm_n,
    int64_t gemm_k,
    int num_experts,
    const std::string& activation_type = "none") {
    
    // Check if we're on a cc70 compatible device
    if (!is_cc70_compatible_device()) {
        PD_THROW("MOE cc70 compatibility mode requires CUDA compute capability >= 7.0");
    }
    
    // Create output tensor
    auto C = paddle::empty({total_rows, gemm_n}, A.dtype(), A.place());
    
    cudaStream_t stream = A.stream();
    cudaError_t status = cudaSuccess;
    
    // Dispatch based on data type and quantization
    if (A.dtype() == paddle::DataType::FLOAT16) {
        if (weight_scales.has_value()) {
            // INT8 quantized path
            status = moe_gemm_int8_cc70(
                A.data(), B.data(), weight_scales->data(), C.data(),
                bias.has_value() ? bias->data() : nullptr,
                total_rows_before_expert.data<int64_t>(),
                total_rows, gemm_n, gemm_k, num_experts, stream);
        } else if (activation_type != "none" && bias.has_value()) {
            // FP16 with bias and activation
            status = moe_gemm_bias_act_cc70(
                A.data(), B.data(), nullptr, bias->data(), C.data(),
                total_rows_before_expert.data<int64_t>(),
                total_rows, gemm_n, gemm_k, num_experts,
                activation_type.c_str(), stream);
        } else {
            // Plain FP16 GEMM
            status = moe_gemm_fp16_cc70(
                A.data(), B.data(), C.data(),
                bias.has_value() ? bias->data() : nullptr,
                total_rows_before_expert.data<int64_t>(),
                total_rows, gemm_n, gemm_k, num_experts, stream);
        }
    } else {
        PD_THROW("Unsupported data type for MOE cc70 compatibility mode. Only FLOAT16 is supported.");
    }
    
    if (status != cudaSuccess) {
        PD_THROW("MOE GEMM cc70 kernel launch failed: ", cudaGetErrorString(status));
    }
    
    return {C};
}

// Register the custom operator
PD_BUILD_OP(moe_gemm_cc70)
    .Inputs({"A", "B", paddle::Optional("weight_scales"), paddle::Optional("bias"), "total_rows_before_expert"})
    .Outputs({"C"})
    .Attrs({"total_rows: int64_t", "gemm_n: int64_t", "gemm_k: int64_t", 
            "num_experts: int", "activation_type: std::string"})
    .SetKernelFn(PD_KERNEL(moe_gemm_cc70_op));

#endif // MOE_COMPATIBILITY_CC70
