// Copyright (c) 2025 PaddlePaddle Authors. All Rights Reserved.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

#pragma once

#include "cutlass/cutlass.h"
#include "cutlass/gemm/device/gemm.h"
#include "cutlass/gemm/device/gemm_grouped.h"
#include "cutlass/arch/arch.h"
#include "cutlass/arch/mma.h"
#include "cutlass/layout/matrix.h"
#include "cutlass/numeric_types.h"

#ifdef MOE_COMPATIBILITY_CC70

namespace cutlass {
namespace gemm {
namespace kernel {

// Compatibility GEMM configuration for cc70
template <
    typename ElementA_,
    typename LayoutA_,
    typename ElementB_,
    typename LayoutB_,
    typename ElementC_,
    typename LayoutC_,
    typename ElementAccumulator_ = float,
    typename OperatorClass_ = arch::OpClassSimt,  // Use SIMT for cc70
    typename ArchTag_ = arch::Sm70,
    typename ThreadblockShape_ = cutlass::gemm::GemmShape<64, 64, 8>,  // Smaller tiles for cc70
    typename WarpShape_ = cutlass::gemm::GemmShape<32, 32, 8>,
    typename InstructionShape_ = cutlass::gemm::GemmShape<1, 1, 1>,
    int Stages = 2  // Fewer stages for cc70
>
struct MoeGemmCC70Compat {
    using ElementA = ElementA_;
    using LayoutA = LayoutA_;
    using ElementB = ElementB_;
    using LayoutB = LayoutB_;
    using ElementC = ElementC_;
    using LayoutC = LayoutC_;
    using ElementAccumulator = ElementAccumulator_;
    using OperatorClass = OperatorClass_;
    using ArchTag = ArchTag_;
    using ThreadblockShape = ThreadblockShape_;
    using WarpShape = WarpShape_;
    using InstructionShape = InstructionShape_;
    static int const kStages = Stages;

    // Ensure we're targeting the right architecture
    static_assert(ArchTag::kMinComputeCapability <= 70,
                  "This kernel is designed for cc70 compatibility");

    // Use SIMT operations for cc70
    using Operator = typename cutlass::platform::conditional<
        cutlass::platform::is_same<OperatorClass, cutlass::arch::OpClassTensorOp>::value,
        cutlass::arch::OpMultiplyAdd,  // Fallback to SIMT multiply-add
        cutlass::arch::OpMultiplyAdd
    >::type;
    
    // Define the GEMM kernel
    using GemmKernel = typename cutlass::gemm::kernel::DefaultGemm<
        ElementA, LayoutA, cutlass::ComplexTransform::kNone, 8,  // Alignment
        ElementB, LayoutB, cutlass::ComplexTransform::kNone, 8,  // Alignment
        ElementC, LayoutC,
        ElementAccumulator,
        OperatorClass,
        ArchTag,
        ThreadblockShape,
        WarpShape,
        InstructionShape,
        cutlass::epilogue::thread::LinearCombination<
            ElementC,
            128 / cutlass::sizeof_bits<ElementC>::value,
            ElementAccumulator,
            ElementAccumulator
        >,
        cutlass::gemm::threadblock::GemmIdentityThreadblockSwizzle<>,
        kStages,
        Operator
    >::GemmKernel;
    
    // Define the device-level GEMM
    using Gemm = cutlass::gemm::device::Gemm<
        ElementA, LayoutA,
        ElementB, LayoutB,
        ElementC, LayoutC,
        ElementAccumulator,
        OperatorClass,
        ArchTag,
        ThreadblockShape,
        WarpShape,
        InstructionShape,
        cutlass::epilogue::thread::LinearCombination<
            ElementC,
            128 / cutlass::sizeof_bits<ElementC>::value,
            ElementAccumulator,
            ElementAccumulator
        >,
        cutlass::gemm::threadblock::GemmIdentityThreadblockSwizzle<>,
        kStages,
        Operator
    >;
};

// Specialized configuration for FP16 on cc70
using MoeGemmCC70FP16 = MoeGemmCC70Compat<
    cutlass::half_t,                    // ElementA
    cutlass::layout::RowMajor,          // LayoutA
    cutlass::half_t,                    // ElementB
    cutlass::layout::ColumnMajor,       // LayoutB
    cutlass::half_t,                    // ElementC
    cutlass::layout::RowMajor,          // LayoutC
    float,                              // ElementAccumulator
    cutlass::arch::OpClassSimt,         // OperatorClass (SIMT for cc70)
    cutlass::arch::Sm70,                // ArchTag
    cutlass::gemm::GemmShape<64, 64, 8>, // ThreadblockShape
    cutlass::gemm::GemmShape<32, 32, 8>, // WarpShape
    cutlass::gemm::GemmShape<1, 1, 1>,   // InstructionShape
    2                                   // Stages
>;

// Specialized configuration for INT8 quantized weights on cc70
using MoeGemmCC70INT8 = MoeGemmCC70Compat<
    cutlass::half_t,                    // ElementA (activations in FP16)
    cutlass::layout::RowMajor,          // LayoutA
    int8_t,                             // ElementB (weights in INT8)
    cutlass::layout::ColumnMajor,       // LayoutB
    cutlass::half_t,                    // ElementC
    cutlass::layout::RowMajor,          // LayoutC
    int32_t,                            // ElementAccumulator (INT32 for INT8 ops)
    cutlass::arch::OpClassSimt,         // OperatorClass
    cutlass::arch::Sm70,                // ArchTag
    cutlass::gemm::GemmShape<64, 64, 16>, // ThreadblockShape (larger K for INT8)
    cutlass::gemm::GemmShape<32, 32, 16>, // WarpShape
    cutlass::gemm::GemmShape<1, 1, 4>,    // InstructionShape (4 INT8 elements)
    2                                   // Stages
>;

// Helper function to check if cc70 compatibility mode is needed
__host__ __device__ constexpr bool needs_cc70_compatibility() {
#ifdef MOE_COMPATIBILITY_CC70
    return true;
#else
    return false;
#endif
}

// Runtime compute capability check
inline bool is_cc70_device() {
    int device;
    cudaGetDevice(&device);
    
    cudaDeviceProp prop;
    cudaGetDeviceProperties(&prop, device);
    
    int cc = prop.major * 10 + prop.minor;
    return cc >= 70 && cc < 80;
}

// Dispatch function for cc70 compatible MOE GEMM
template<typename T>
cudaError_t dispatch_moe_gemm_cc70(
    const T* A,
    const T* B,
    T* C,
    const T* bias,
    int M, int N, int K,
    const int* expert_ids,
    int num_experts,
    cudaStream_t stream) {
    
    if constexpr (std::is_same_v<T, cutlass::half_t>) {
        // Use FP16 GEMM for cc70
        typename MoeGemmCC70FP16::Gemm gemm_op;
        
        typename MoeGemmCC70FP16::Gemm::Arguments args{
            {M, N, K},                    // Problem size
            {A, K},                       // TensorRef A
            {B, K},                       // TensorRef B  
            {C, N},                       // TensorRef C
            {C, N},                       // TensorRef D
            {1.0f, 0.0f}                  // Epilogue args (alpha, beta)
        };
        
        // Launch the GEMM
        auto status = gemm_op(args, nullptr, stream);
        return (status == cutlass::Status::kSuccess) ? cudaSuccess : cudaErrorLaunchFailure;
    } else {
        // Unsupported type for cc70 compatibility
        return cudaErrorNotSupported;
    }
}

// Grouped GEMM dispatch for multiple experts
template<typename T>
cudaError_t dispatch_moe_grouped_gemm_cc70(
    const T** A_array,
    const T** B_array,
    T** C_array,
    const T** bias_array,
    const int* M_array,
    const int* N_array,
    const int* K_array,
    int group_count,
    cudaStream_t stream) {
    
    // For cc70, we'll execute GEMMs sequentially rather than using grouped GEMM
    // This is less efficient but more compatible
    for (int i = 0; i < group_count; ++i) {
        auto status = dispatch_moe_gemm_cc70<T>(
            A_array[i], B_array[i], C_array[i], bias_array[i],
            M_array[i], N_array[i], K_array[i],
            nullptr, 1, stream);
        
        if (status != cudaSuccess) {
            return status;
        }
    }
    
    return cudaSuccess;
}

} // namespace kernel
} // namespace gemm
} // namespace cutlass

#endif // MOE_COMPATIBILITY_CC70
