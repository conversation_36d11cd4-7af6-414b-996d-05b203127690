// auto generated by generate.py
// clang-format off

#include "moe/moe_wna16_marlin_utils/kernel.h"
#include "moe/moe_wna16_marlin_utils/marlin_template.h"

namespace MARLIN_NAMESPACE_NAME {

template __global__ void Marlin<nv_bfloat16, MARLIN_NAMESPACE_NAME::kU4B8.id(), 256, 1, 8, 8, true, pipe_stages, -1, false>( MARLIN_KERNEL_PARAMS );

template __global__ void Marlin<nv_bfloat16, MARLIN_NAMESPACE_NAME::kU4B8.id(), 128, 1, 8, 4, true, pipe_stages, -1, false>( MARLIN_KERNEL_PARAMS );

template __global__ void Marlin<nv_bfloat16, MARLIN_NAMESPACE_NAME::kU4B8.id(), 256, 1, 8, 8, false, pipe_stages, -1, false>( MARLIN_KERNEL_PARAMS );

template __global__ void Marlin<nv_bfloat16, MARLIN_NAMESPACE_NAME::kU4B8.id(), 128, 1, 8, 4, false, pipe_stages, -1, false>( MARLIN_KERNEL_PARAMS );

template __global__ void Marlin<nv_bfloat16, MARLIN_NAMESPACE_NAME::kU4B8.id(), 256, 2, 16, 4, false, pipe_stages, -1, false>( MARLIN_KERNEL_PARAMS );

template __global__ void Marlin<nv_bfloat16, MARLIN_NAMESPACE_NAME::kU4B8.id(), 128, 2, 8, 4, false, pipe_stages, -1, false>( MARLIN_KERNEL_PARAMS );

template __global__ void Marlin<nv_bfloat16, MARLIN_NAMESPACE_NAME::kU4B8.id(), 256, 3, 16, 4, false, pipe_stages, -1, false>( MARLIN_KERNEL_PARAMS );

template __global__ void Marlin<nv_bfloat16, MARLIN_NAMESPACE_NAME::kU4B8.id(), 128, 3, 8, 4, false, pipe_stages, -1, false>( MARLIN_KERNEL_PARAMS );

template __global__ void Marlin<nv_bfloat16, MARLIN_NAMESPACE_NAME::kU4B8.id(), 256, 4, 16, 4, false, pipe_stages, -1, false>( MARLIN_KERNEL_PARAMS );

template __global__ void Marlin<nv_bfloat16, MARLIN_NAMESPACE_NAME::kU4B8.id(), 128, 4, 8, 4, false, pipe_stages, -1, false>( MARLIN_KERNEL_PARAMS );

template __global__ void Marlin<nv_bfloat16, MARLIN_NAMESPACE_NAME::kU4B8.id(), 256, 1, 8, 8, true, pipe_stages, 2, false>( MARLIN_KERNEL_PARAMS );

template __global__ void Marlin<nv_bfloat16, MARLIN_NAMESPACE_NAME::kU4B8.id(), 128, 1, 8, 4, true, pipe_stages, 2, false>( MARLIN_KERNEL_PARAMS );

template __global__ void Marlin<nv_bfloat16, MARLIN_NAMESPACE_NAME::kU4B8.id(), 256, 1, 8, 8, false, pipe_stages, 2, false>( MARLIN_KERNEL_PARAMS );

template __global__ void Marlin<nv_bfloat16, MARLIN_NAMESPACE_NAME::kU4B8.id(), 128, 1, 8, 4, false, pipe_stages, 2, false>( MARLIN_KERNEL_PARAMS );

template __global__ void Marlin<nv_bfloat16, MARLIN_NAMESPACE_NAME::kU4B8.id(), 256, 2, 16, 4, false, pipe_stages, 2, false>( MARLIN_KERNEL_PARAMS );

template __global__ void Marlin<nv_bfloat16, MARLIN_NAMESPACE_NAME::kU4B8.id(), 128, 2, 8, 4, false, pipe_stages, 2, false>( MARLIN_KERNEL_PARAMS );

template __global__ void Marlin<nv_bfloat16, MARLIN_NAMESPACE_NAME::kU4B8.id(), 256, 3, 16, 4, false, pipe_stages, 2, false>( MARLIN_KERNEL_PARAMS );

template __global__ void Marlin<nv_bfloat16, MARLIN_NAMESPACE_NAME::kU4B8.id(), 128, 3, 8, 4, false, pipe_stages, 2, false>( MARLIN_KERNEL_PARAMS );

template __global__ void Marlin<nv_bfloat16, MARLIN_NAMESPACE_NAME::kU4B8.id(), 256, 4, 16, 4, false, pipe_stages, 2, false>( MARLIN_KERNEL_PARAMS );

template __global__ void Marlin<nv_bfloat16, MARLIN_NAMESPACE_NAME::kU4B8.id(), 128, 4, 8, 4, false, pipe_stages, 2, false>( MARLIN_KERNEL_PARAMS );

template __global__ void Marlin<nv_bfloat16, MARLIN_NAMESPACE_NAME::kU4B8.id(), 256, 1, 8, 8, true, pipe_stages, 4, false>( MARLIN_KERNEL_PARAMS );

template __global__ void Marlin<nv_bfloat16, MARLIN_NAMESPACE_NAME::kU4B8.id(), 128, 1, 8, 4, true, pipe_stages, 4, false>( MARLIN_KERNEL_PARAMS );

template __global__ void Marlin<nv_bfloat16, MARLIN_NAMESPACE_NAME::kU4B8.id(), 256, 1, 8, 8, false, pipe_stages, 4, false>( MARLIN_KERNEL_PARAMS );

template __global__ void Marlin<nv_bfloat16, MARLIN_NAMESPACE_NAME::kU4B8.id(), 128, 1, 8, 4, false, pipe_stages, 4, false>( MARLIN_KERNEL_PARAMS );

template __global__ void Marlin<nv_bfloat16, MARLIN_NAMESPACE_NAME::kU4B8.id(), 256, 2, 16, 4, false, pipe_stages, 4, false>( MARLIN_KERNEL_PARAMS );

template __global__ void Marlin<nv_bfloat16, MARLIN_NAMESPACE_NAME::kU4B8.id(), 128, 2, 8, 4, false, pipe_stages, 4, false>( MARLIN_KERNEL_PARAMS );

template __global__ void Marlin<nv_bfloat16, MARLIN_NAMESPACE_NAME::kU4B8.id(), 256, 3, 16, 4, false, pipe_stages, 4, false>( MARLIN_KERNEL_PARAMS );

template __global__ void Marlin<nv_bfloat16, MARLIN_NAMESPACE_NAME::kU4B8.id(), 128, 3, 8, 4, false, pipe_stages, 4, false>( MARLIN_KERNEL_PARAMS );

template __global__ void Marlin<nv_bfloat16, MARLIN_NAMESPACE_NAME::kU4B8.id(), 256, 4, 16, 4, false, pipe_stages, 4, false>( MARLIN_KERNEL_PARAMS );

template __global__ void Marlin<nv_bfloat16, MARLIN_NAMESPACE_NAME::kU4B8.id(), 128, 4, 8, 4, false, pipe_stages, 4, false>( MARLIN_KERNEL_PARAMS );

template __global__ void Marlin<nv_bfloat16, MARLIN_NAMESPACE_NAME::kU4B8.id(), 256, 1, 8, 8, true, pipe_stages, 8, false>( MARLIN_KERNEL_PARAMS );

template __global__ void Marlin<nv_bfloat16, MARLIN_NAMESPACE_NAME::kU4B8.id(), 128, 1, 8, 4, true, pipe_stages, 8, false>( MARLIN_KERNEL_PARAMS );

template __global__ void Marlin<nv_bfloat16, MARLIN_NAMESPACE_NAME::kU4B8.id(), 256, 1, 8, 8, false, pipe_stages, 8, false>( MARLIN_KERNEL_PARAMS );

template __global__ void Marlin<nv_bfloat16, MARLIN_NAMESPACE_NAME::kU4B8.id(), 128, 1, 8, 4, false, pipe_stages, 8, false>( MARLIN_KERNEL_PARAMS );

template __global__ void Marlin<nv_bfloat16, MARLIN_NAMESPACE_NAME::kU4B8.id(), 256, 2, 16, 4, false, pipe_stages, 8, false>( MARLIN_KERNEL_PARAMS );

template __global__ void Marlin<nv_bfloat16, MARLIN_NAMESPACE_NAME::kU4B8.id(), 128, 2, 8, 4, false, pipe_stages, 8, false>( MARLIN_KERNEL_PARAMS );

template __global__ void Marlin<nv_bfloat16, MARLIN_NAMESPACE_NAME::kU4B8.id(), 256, 3, 16, 4, false, pipe_stages, 8, false>( MARLIN_KERNEL_PARAMS );

template __global__ void Marlin<nv_bfloat16, MARLIN_NAMESPACE_NAME::kU4B8.id(), 128, 3, 8, 4, false, pipe_stages, 8, false>( MARLIN_KERNEL_PARAMS );

template __global__ void Marlin<nv_bfloat16, MARLIN_NAMESPACE_NAME::kU4B8.id(), 256, 4, 16, 4, false, pipe_stages, 8, false>( MARLIN_KERNEL_PARAMS );

template __global__ void Marlin<nv_bfloat16, MARLIN_NAMESPACE_NAME::kU4B8.id(), 128, 4, 8, 4, false, pipe_stages, 8, false>( MARLIN_KERNEL_PARAMS );

}
