// Copyright (c) 2025 PaddlePaddle Authors. All Rights Reserved.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

/**
 * Simple MOE implementation for cc>=70 compatibility
 * Avoids complex CUTLASS templates and uses basic CUDA kernels
 */

#include "moe_compatibility.h"
#include <cuda_runtime.h>
#include <cuda_fp16.h>
#include <cublas_v2.h>

#ifdef MOE_COMPATIBILITY_CC70

// Simple MOE GEMM kernel for cc70
__global__ void moe_simple_gemm_kernel(
    const __half* __restrict__ A,
    const __half* __restrict__ B,
    __half* __restrict__ C,
    const __half* __restrict__ bias,
    const int64_t* __restrict__ expert_offsets,
    int M, int N, int K, int num_experts) {
    
    int tid = blockIdx.x * blockDim.x + threadIdx.x;
    int total_elements = M * N;
    
    if (tid >= total_elements) return;
    
    int row = tid / N;
    int col = tid % N;
    
    // Find which expert this row belongs to
    int expert_id = 0;
    for (int i = 0; i < num_experts; ++i) {
        if (row < expert_offsets[i]) {
            expert_id = i;
            break;
        }
    }
    
    // Calculate expert-specific offsets
    int expert_start = (expert_id == 0) ? 0 : expert_offsets[expert_id - 1];
    int local_row = row - expert_start;
    int expert_B_offset = expert_id * K * N;
    int expert_bias_offset = expert_id * N;
    
    // Compute dot product
    float sum = 0.0f;
    for (int k = 0; k < K; ++k) {
        float a_val = __half2float(A[row * K + k]);
        float b_val = __half2float(B[expert_B_offset + k * N + col]);
        sum += a_val * b_val;
    }
    
    // Add bias if provided
    if (bias != nullptr) {
        sum += __half2float(bias[expert_bias_offset + col]);
    }
    
    C[tid] = __float2half(sum);
}

// Optimized shared memory version for larger matrices
__global__ void moe_shared_gemm_kernel(
    const __half* __restrict__ A,
    const __half* __restrict__ B,
    __half* __restrict__ C,
    const __half* __restrict__ bias,
    const int64_t* __restrict__ expert_offsets,
    int M, int N, int K, int num_experts) {
    
    const int TILE_SIZE = 16;
    __shared__ __half shared_A[TILE_SIZE][TILE_SIZE];
    __shared__ __half shared_B[TILE_SIZE][TILE_SIZE];
    
    int bx = blockIdx.x, by = blockIdx.y;
    int tx = threadIdx.x, ty = threadIdx.y;
    
    int row = by * TILE_SIZE + ty;
    int col = bx * TILE_SIZE + tx;
    
    if (row >= M || col >= N) return;
    
    // Find expert for this row
    int expert_id = 0;
    for (int i = 0; i < num_experts; ++i) {
        if (row < expert_offsets[i]) {
            expert_id = i;
            break;
        }
    }
    
    int expert_B_offset = expert_id * K * N;
    int expert_bias_offset = expert_id * N;
    
    float sum = 0.0f;
    
    // Tile-based computation
    for (int tile = 0; tile < (K + TILE_SIZE - 1) / TILE_SIZE; ++tile) {
        // Load A tile
        int k_idx = tile * TILE_SIZE + tx;
        if (k_idx < K && row < M) {
            shared_A[ty][tx] = A[row * K + k_idx];
        } else {
            shared_A[ty][tx] = __float2half(0.0f);
        }
        
        // Load B tile
        int k_idx_b = tile * TILE_SIZE + ty;
        if (k_idx_b < K && col < N) {
            shared_B[ty][tx] = B[expert_B_offset + k_idx_b * N + col];
        } else {
            shared_B[ty][tx] = __float2half(0.0f);
        }
        
        __syncthreads();
        
        // Compute partial sum
        for (int k = 0; k < TILE_SIZE; ++k) {
            sum += __half2float(shared_A[ty][k]) * __half2float(shared_B[k][tx]);
        }
        
        __syncthreads();
    }
    
    // Add bias
    if (bias != nullptr && col < N) {
        sum += __half2float(bias[expert_bias_offset + col]);
    }
    
    if (row < M && col < N) {
        C[row * N + col] = __float2half(sum);
    }
}

// Host function to launch MOE GEMM
extern "C" cudaError_t launch_moe_simple_gemm_cc70(
    const void* A,
    const void* B,
    void* C,
    const void* bias,
    const int64_t* expert_offsets,
    int M, int N, int K, int num_experts,
    cudaStream_t stream) {
    
    const __half* A_fp16 = static_cast<const __half*>(A);
    const __half* B_fp16 = static_cast<const __half*>(B);
    __half* C_fp16 = static_cast<__half*>(C);
    const __half* bias_fp16 = static_cast<const __half*>(bias);
    
    if (M >= 16 && N >= 16) {
        // Use shared memory kernel for larger matrices
        const int TILE_SIZE = 16;
        dim3 block(TILE_SIZE, TILE_SIZE);
        dim3 grid((N + TILE_SIZE - 1) / TILE_SIZE, (M + TILE_SIZE - 1) / TILE_SIZE);
        
        moe_shared_gemm_kernel<<<grid, block, 0, stream>>>(
            A_fp16, B_fp16, C_fp16, bias_fp16, expert_offsets,
            M, N, K, num_experts);
    } else {
        // Use simple kernel for smaller matrices
        const int threads_per_block = 256;
        const int total_elements = M * N;
        const int blocks = (total_elements + threads_per_block - 1) / threads_per_block;
        
        moe_simple_gemm_kernel<<<blocks, threads_per_block, 0, stream>>>(
            A_fp16, B_fp16, C_fp16, bias_fp16, expert_offsets,
            M, N, K, num_experts);
    }
    
    return cudaGetLastError();
}

// CUBLAS-based fallback for better performance
extern "C" cudaError_t launch_moe_cublas_cc70(
    const void* A,
    const void* B,
    void* C,
    const void* bias,
    const int64_t* expert_offsets,
    int M, int N, int K, int num_experts,
    cudaStream_t stream) {
    
    cublasHandle_t handle;
    cublasStatus_t status = cublasCreate(&handle);
    if (status != CUBLAS_STATUS_SUCCESS) {
        return cudaErrorInitializationError;
    }
    
    cublasSetStream(handle, stream);
    
    const __half* A_fp16 = static_cast<const __half*>(A);
    const __half* B_fp16 = static_cast<const __half*>(B);
    __half* C_fp16 = static_cast<__half*>(C);
    
    const __half alpha = __float2half(1.0f);
    const __half beta = __float2half(0.0f);
    
    // Process each expert separately
    for (int expert_id = 0; expert_id < num_experts; ++expert_id) {
        int64_t expert_start = (expert_id == 0) ? 0 : expert_offsets[expert_id - 1];
        int64_t expert_end = expert_offsets[expert_id];
        int expert_rows = expert_end - expert_start;
        
        if (expert_rows <= 0) continue;
        
        const __half* A_expert = A_fp16 + expert_start * K;
        const __half* B_expert = B_fp16 + expert_id * K * N;
        __half* C_expert = C_fp16 + expert_start * N;
        
        // Use HGEMM for FP16 computation
        status = cublasHgemm(handle, CUBLAS_OP_N, CUBLAS_OP_N,
                            N, expert_rows, K,
                            &alpha,
                            B_expert, N,
                            A_expert, K,
                            &beta,
                            C_expert, N);
        
        if (status != CUBLAS_STATUS_SUCCESS) {
            cublasDestroy(handle);
            return cudaErrorLaunchFailure;
        }
    }
    
    cublasDestroy(handle);
    return cudaSuccess;
}

// Paddle custom operator wrapper
#include "paddle/extension.h"

std::vector<paddle::Tensor> moe_simple_cc70_op(
    const paddle::Tensor& A,
    const paddle::Tensor& B,
    const paddle::Tensor& expert_offsets,
    int64_t M, int64_t N, int64_t K, int num_experts,
    bool use_cublas = true) {
    
    // Create output tensor
    auto C = paddle::empty({M, N}, A.dtype(), A.place());
    
    cudaStream_t stream = A.stream();
    cudaError_t result;
    
    if (use_cublas) {
        result = launch_moe_cublas_cc70(
            A.data(), B.data(), C.data(), nullptr,
            expert_offsets.data<int64_t>(),
            M, N, K, num_experts, stream);
    } else {
        result = launch_moe_simple_gemm_cc70(
            A.data(), B.data(), C.data(), nullptr,
            expert_offsets.data<int64_t>(),
            M, N, K, num_experts, stream);
    }
    
    if (result != cudaSuccess) {
        PD_THROW("MOE simple cc70 kernel failed: ", cudaGetErrorString(result));
    }
    
    return {C};
}

// Register the operator
PD_BUILD_OP(moe_simple_cc70)
    .Inputs({"A", "B", "expert_offsets"})
    .Outputs({"C"})
    .Attrs({"M: int64_t", "N: int64_t", "K: int64_t", "num_experts: int", "use_cublas: bool"})
    .SetKernelFn(PD_KERNEL(moe_simple_cc70_op));

#endif // MOE_COMPATIBILITY_CC70
