#!/usr/bin/env python3
# Copyright (c) 2025 PaddlePaddle Authors. All Rights Reserved.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

"""
Test script for MOE cc>=70 compatibility implementation.
This script tests the compatibility layer for running MOE operations
on CUDA compute capability >= 70 devices.
"""

import os
import sys
import paddle
import numpy as np
from typing import Optional

def get_compute_capability():
    """Get CUDA compute capability of current device."""
    try:
        if paddle.is_compiled_with_cuda():
            device_id = paddle.get_device().split(':')[-1]
            prop = paddle.device.cuda.get_device_properties(int(device_id))
            return prop.major * 10 + prop.minor
    except Exception as e:
        print(f"Failed to get compute capability: {e}")
        return None
    return None

def test_moe_compatibility_detection():
    """Test MOE compatibility detection logic."""
    print("=== Testing MOE Compatibility Detection ===")
    
    cc = get_compute_capability()
    if cc is None:
        print("❌ Could not detect compute capability")
        return False
    
    print(f"✅ Detected compute capability: {cc}")
    
    # Test the MOE method selection
    try:
        from fastdeploy.model_executor.layers.moe.moe import get_moe_method
        moe_method = get_moe_method()
        print(f"✅ Selected MOE method: {type(moe_method).__name__}")
        
        if cc >= 80:
            expected_type = "CutlassMoEMethod"
        elif cc >= 70:
            expected_type = "CC70CompatMoEMethod"
        else:
            expected_type = "CC70CompatMoEMethod"  # Fallback
        
        if expected_type in type(moe_method).__name__:
            print(f"✅ Correct MOE method selected for cc{cc}")
            return True
        else:
            print(f"❌ Unexpected MOE method for cc{cc}: {type(moe_method).__name__}")
            return False
            
    except Exception as e:
        print(f"❌ Failed to get MOE method: {e}")
        return False

def test_compatibility_headers():
    """Test that compatibility headers are properly included."""
    print("\n=== Testing Compatibility Headers ===")
    
    # Check if compatibility files exist
    compatibility_files = [
        "custom_ops/gpu_ops/moe/moe_compatibility.h",
        "custom_ops/gpu_ops/moe/moe_gemm_cc70_compat.cu",
        "custom_ops/gpu_ops/cutlass_kernels/moe_gemm/fused_moe_gemm_kernels_cc70_compat.h",
        "custom_ops/gpu_ops/cutlass_kernels/moe_gemm/fused_moe_gemm_kernels_cc70_compat.cu",
        "fastdeploy/model_executor/layers/moe/fused_moe_cc70_backend.py"
    ]
    
    all_exist = True
    for file_path in compatibility_files:
        if os.path.exists(file_path):
            print(f"✅ Found: {file_path}")
        else:
            print(f"❌ Missing: {file_path}")
            all_exist = False
    
    return all_exist

def test_basic_moe_operation():
    """Test basic MOE operation with compatibility layer."""
    print("\n=== Testing Basic MOE Operation ===")
    
    try:
        # Set up test parameters
        batch_size = 4
        seq_len = 128
        hidden_size = 512
        num_experts = 8
        top_k = 2
        intermediate_size = 2048
        
        # Create test data
        paddle.set_device("gpu:0")
        
        # Input tensor (use FP16 for cc70 compatibility)
        x = paddle.randn([batch_size, seq_len, hidden_size], dtype=paddle.float16)
        
        # Router logits
        router_logits = paddle.randn([batch_size * seq_len, num_experts], dtype=paddle.float16)
        
        print(f"✅ Created test tensors: x.shape={x.shape}, router_logits.shape={router_logits.shape}")
        
        # Test MOE method instantiation
        from fastdeploy.model_executor.layers.moe.moe import get_moe_method
        moe_method = get_moe_method()
        
        print(f"✅ Instantiated MOE method: {type(moe_method).__name__}")
        
        # Create a mock MOE layer for testing
        class MockMoELayer:
            def __init__(self):
                self.num_experts = num_experts
                self.top_k = top_k
                self.hidden_size = hidden_size
                self.intermediate_size = intermediate_size
                self.precision = "fp16"
                self.quant_method = None
                
                # Create mock experts
                self.experts = []
                for i in range(num_experts):
                    expert = MockExpert(hidden_size, intermediate_size)
                    self.experts.append(expert)
        
        class MockExpert:
            def __init__(self, hidden_size, intermediate_size):
                self.gate_proj = MockLinear(hidden_size, intermediate_size)
                self.up_proj = MockLinear(hidden_size, intermediate_size)
                self.down_proj = MockLinear(intermediate_size, hidden_size)
        
        class MockLinear:
            def __init__(self, in_features, out_features):
                self.weight = paddle.randn([out_features, in_features], dtype=paddle.float16)
        
        # Test compatibility check
        mock_layer = MockMoELayer()
        can_implement = moe_method.can_implement(mock_layer)
        print(f"✅ MOE method can implement layer: {can_implement}")
        
        if can_implement:
            print("✅ Basic MOE compatibility test passed")
            return True
        else:
            print("❌ MOE method cannot implement test layer")
            return False
            
    except Exception as e:
        print(f"❌ Basic MOE operation test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_precision_fallback():
    """Test precision fallback from BF16 to FP16."""
    print("\n=== Testing Precision Fallback ===")
    
    try:
        cc = get_compute_capability()
        
        if cc and cc < 80:
            # Test BF16 to FP16 fallback
            if paddle.is_compiled_with_cuda():
                # Create BF16 tensor if supported
                try:
                    x_bf16 = paddle.randn([4, 128], dtype=paddle.bfloat16)
                    x_fp16 = x_bf16.cast(paddle.float16)
                    print("✅ BF16 to FP16 conversion successful")
                    return True
                except:
                    print("✅ BF16 not supported, using FP16 directly (expected for cc<80)")
                    return True
            else:
                print("✅ CUDA not available, skipping precision test")
                return True
        else:
            print("✅ cc>=80 detected, BF16 should be natively supported")
            return True
            
    except Exception as e:
        print(f"❌ Precision fallback test failed: {e}")
        return False

def test_quantization_support():
    """Test quantization support levels for different compute capabilities."""
    print("\n=== Testing Quantization Support ===")
    
    try:
        cc = get_compute_capability()
        
        if cc is None:
            print("⚠️  Could not detect compute capability, skipping quantization test")
            return True
        
        # Test quantization support based on compute capability
        if cc >= 80:
            print("✅ cc>=80: Should support INT4, INT8, and BF16")
            expected_support = {"int4": True, "int8": True, "bf16": True}
        elif cc >= 75:
            print("✅ cc>=75: Should support INT4, INT8, but not BF16")
            expected_support = {"int4": True, "int8": True, "bf16": False}
        elif cc >= 70:
            print("✅ cc>=70: Should support INT8 only")
            expected_support = {"int4": False, "int8": True, "bf16": False}
        else:
            print("✅ cc<70: Limited quantization support")
            expected_support = {"int4": False, "int8": False, "bf16": False}
        
        # Test the compatibility backend
        from fastdeploy.model_executor.layers.moe.fused_moe_cc70_backend import CC70CompatMoEMethod
        compat_method = CC70CompatMoEMethod(None)
        
        print(f"✅ CC70 compatibility method supports_int8: {compat_method.supports_int8 if hasattr(compat_method, 'supports_int8') else 'N/A'}")
        print(f"✅ CC70 compatibility method supports_bf16: {compat_method.supports_bf16}")
        
        return True
        
    except Exception as e:
        print(f"❌ Quantization support test failed: {e}")
        return False

def main():
    """Run all compatibility tests."""
    print("MOE cc>=70 Compatibility Test Suite")
    print("=" * 50)
    
    # Check if CUDA is available
    if not paddle.is_compiled_with_cuda():
        print("❌ CUDA is not available. Skipping tests.")
        return False
    
    # Run all tests
    tests = [
        ("Compatibility Detection", test_moe_compatibility_detection),
        ("Compatibility Headers", test_compatibility_headers),
        ("Basic MOE Operation", test_basic_moe_operation),
        ("Precision Fallback", test_precision_fallback),
        ("Quantization Support", test_quantization_support),
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ Test '{test_name}' crashed: {e}")
            results.append((test_name, False))
    
    # Print summary
    print("\n" + "=" * 50)
    print("Test Summary:")
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"  {status}: {test_name}")
        if result:
            passed += 1
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! MOE cc>=70 compatibility is working.")
        return True
    else:
        print("⚠️  Some tests failed. Please check the implementation.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
