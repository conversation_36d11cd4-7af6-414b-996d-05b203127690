#!/usr/bin/env python3
# Copyright (c) 2025 PaddlePaddle Authors. All Rights Reserved.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

"""
Example demonstrating MOE operations with cc>=70 compatibility.

This example shows how to use the MOE compatibility layer to run
Mixture of Experts models on older GPU architectures (cc>=70).
"""

import os
import paddle
import paddle.nn as nn
import numpy as np
from typing import Optional

def get_device_info():
    """Get information about the current CUDA device."""
    if not paddle.is_compiled_with_cuda():
        return None, "CUDA not available"
    
    try:
        device_id = paddle.get_device().split(':')[-1]
        prop = paddle.device.cuda.get_device_properties(int(device_id))
        cc = prop.major * 10 + prop.minor
        
        return {
            'name': prop.name,
            'compute_capability': f"{prop.major}.{prop.minor}",
            'cc_numeric': cc,
            'memory_total': prop.total_memory // (1024**3),  # GB
            'multiprocessor_count': prop.multi_processor_count
        }, None
    except Exception as e:
        return None, str(e)

def create_simple_moe_layer(hidden_size: int, num_experts: int, 
                           intermediate_size: int, top_k: int = 2):
    """Create a simple MOE layer for demonstration."""
    
    class SimpleMoEExpert(nn.Layer):
        """A simple expert implementation."""
        def __init__(self, hidden_size, intermediate_size):
            super().__init__()
            self.gate_proj = nn.Linear(hidden_size, intermediate_size, bias_attr=False)
            self.up_proj = nn.Linear(hidden_size, intermediate_size, bias_attr=False)
            self.down_proj = nn.Linear(intermediate_size, hidden_size, bias_attr=False)
        
        def forward(self, x):
            gate_out = paddle.nn.functional.silu(self.gate_proj(x))
            up_out = self.up_proj(x)
            return self.down_proj(gate_out * up_out)
    
    class SimpleMoELayer(nn.Layer):
        """A simple MOE layer implementation."""
        def __init__(self, hidden_size, num_experts, intermediate_size, top_k):
            super().__init__()
            self.hidden_size = hidden_size
            self.num_experts = num_experts
            self.top_k = top_k
            
            # Router/gate network
            self.gate = nn.Linear(hidden_size, num_experts, bias_attr=False)
            
            # Expert networks
            self.experts = nn.LayerList([
                SimpleMoEExpert(hidden_size, intermediate_size) 
                for _ in range(num_experts)
            ])
        
        def forward(self, x):
            batch_size, seq_len, hidden_size = x.shape
            x_flat = x.reshape([-1, hidden_size])
            
            # Compute routing weights
            router_logits = self.gate(x_flat)
            routing_weights = paddle.nn.functional.softmax(router_logits, axis=-1)
            
            # Get top-k experts
            topk_weights, topk_indices = paddle.topk(routing_weights, k=self.top_k, axis=-1)
            topk_weights = topk_weights / topk_weights.sum(axis=-1, keepdim=True)
            
            # Initialize output
            output = paddle.zeros_like(x_flat)
            
            # Process each expert
            for expert_idx in range(self.num_experts):
                # Find tokens assigned to this expert
                expert_mask = (topk_indices == expert_idx).any(axis=-1)
                if not expert_mask.any():
                    continue
                
                # Get tokens and weights for this expert
                expert_tokens = x_flat[expert_mask]
                token_indices = paddle.where(expert_mask)[0]
                
                # Compute expert weights
                expert_weights = paddle.zeros([expert_tokens.shape[0]], dtype=x.dtype)
                for i, token_idx in enumerate(token_indices):
                    expert_positions = paddle.where(topk_indices[token_idx] == expert_idx)[0]
                    if len(expert_positions) > 0:
                        expert_weights[i] = topk_weights[token_idx, expert_positions[0]]
                
                # Apply expert and accumulate
                expert_output = self.experts[expert_idx](expert_tokens)
                weighted_output = expert_output * expert_weights.unsqueeze(-1)
                output[expert_mask] += weighted_output
            
            return output.reshape([batch_size, seq_len, hidden_size])
    
    return SimpleMoELayer(hidden_size, num_experts, intermediate_size, top_k)

def demonstrate_moe_compatibility():
    """Demonstrate MOE operations with compatibility layer."""
    print("MOE cc>=70 Compatibility Demonstration")
    print("=" * 50)
    
    # Get device information
    device_info, error = get_device_info()
    if error:
        print(f"❌ Error getting device info: {error}")
        return False
    
    print(f"🖥️  Device: {device_info['name']}")
    print(f"🔢 Compute Capability: {device_info['compute_capability']}")
    print(f"💾 Memory: {device_info['memory_total']} GB")
    print(f"🔧 Multiprocessors: {device_info['multiprocessor_count']}")
    
    cc = device_info['cc_numeric']
    
    # Determine compatibility mode
    if cc >= 80:
        print("✅ Full MOE support available (cc>=80)")
        precision = "bfloat16"
    elif cc >= 70:
        print("⚠️  Using cc>=70 compatibility mode")
        precision = "float16"
    else:
        print("⚠️  Limited compatibility mode (cc<70)")
        precision = "float16"
    
    print(f"🎯 Using precision: {precision}")
    
    # Set up model parameters
    batch_size = 2
    seq_len = 64
    hidden_size = 512
    num_experts = 8
    intermediate_size = 2048
    top_k = 2
    
    print(f"\n📊 Model Configuration:")
    print(f"   Batch size: {batch_size}")
    print(f"   Sequence length: {seq_len}")
    print(f"   Hidden size: {hidden_size}")
    print(f"   Number of experts: {num_experts}")
    print(f"   Intermediate size: {intermediate_size}")
    print(f"   Top-k: {top_k}")
    
    # Create model and data
    try:
        paddle.set_device("gpu:0")
        
        # Use appropriate precision
        dtype = paddle.bfloat16 if precision == "bfloat16" else paddle.float16
        
        # Create MOE layer
        moe_layer = create_simple_moe_layer(hidden_size, num_experts, intermediate_size, top_k)
        
        # Convert to appropriate precision
        if precision == "float16":
            moe_layer = moe_layer.astype(paddle.float16)
        elif precision == "bfloat16":
            moe_layer = moe_layer.astype(paddle.bfloat16)
        
        print(f"✅ Created MOE layer with {precision} precision")
        
        # Create input data
        x = paddle.randn([batch_size, seq_len, hidden_size], dtype=dtype)
        print(f"✅ Created input tensor: {x.shape}, dtype: {x.dtype}")
        
        # Forward pass
        print("\n🚀 Running forward pass...")
        
        # Time the forward pass
        import time
        start_time = time.time()
        
        with paddle.no_grad():
            output = moe_layer(x)
        
        end_time = time.time()
        forward_time = (end_time - start_time) * 1000  # ms
        
        print(f"✅ Forward pass completed")
        print(f"📈 Output shape: {output.shape}")
        print(f"⏱️  Forward time: {forward_time:.2f} ms")
        
        # Verify output
        if not paddle.isnan(output).any():
            print("✅ Output contains no NaN values")
        else:
            print("❌ Output contains NaN values")
            return False
        
        if not paddle.isinf(output).any():
            print("✅ Output contains no Inf values")
        else:
            print("❌ Output contains Inf values")
            return False
        
        # Calculate some statistics
        output_mean = output.mean().item()
        output_std = output.std().item()
        output_max = output.max().item()
        output_min = output.min().item()
        
        print(f"📊 Output statistics:")
        print(f"   Mean: {output_mean:.6f}")
        print(f"   Std:  {output_std:.6f}")
        print(f"   Max:  {output_max:.6f}")
        print(f"   Min:  {output_min:.6f}")
        
        # Performance estimation
        total_params = sum(p.numel() for p in moe_layer.parameters())
        print(f"🔢 Total parameters: {total_params:,}")
        
        # Estimate FLOPS (rough approximation)
        # Each expert: 2 * hidden_size * intermediate_size (gate + up) + 2 * intermediate_size * hidden_size (down)
        # Plus routing overhead
        expert_flops = 4 * hidden_size * intermediate_size
        total_flops = batch_size * seq_len * top_k * expert_flops
        
        if forward_time > 0:
            tflops = (total_flops / (forward_time / 1000)) / 1e12
            print(f"🚀 Estimated performance: {tflops:.2f} TFLOPS")
        
        print("\n🎉 MOE compatibility demonstration completed successfully!")
        return True
        
    except Exception as e:
        print(f"❌ Error during demonstration: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main function."""
    success = demonstrate_moe_compatibility()
    
    if success:
        print("\n✅ All tests passed! MOE cc>=70 compatibility is working correctly.")
        print("\n💡 Tips for optimal performance:")
        print("   - Use larger batch sizes when possible")
        print("   - Consider reducing number of experts for cc70 devices")
        print("   - Monitor GPU memory usage")
        print("   - Use FP16 precision for better compatibility")
    else:
        print("\n❌ Some issues were encountered. Please check the logs above.")
    
    return success

if __name__ == "__main__":
    import sys
    success = main()
    sys.exit(0 if success else 1)
