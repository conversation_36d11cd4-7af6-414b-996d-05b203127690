name: Run FastDeploy LogProb Tests
description: "Run FastDeploy LogProb Tests"

on:
  workflow_call:
    inputs:
      DOCKER_IMAGE:
        description: "Build Images"
        required: true
        type: string
        default: "ccr-2vdh3abv-pub.cnc.bj.baidubce.com/paddlepaddle/paddleqa:cuda126-py310"
      PADDLETEST_ARCHIVE_URL:
        description: "URL of the compressed FastDeploy code archive."
        required: true
        type: string
        default: "https://xly-devops.bj.bcebos.com/PaddleTest/PaddleTest.tar.gz"
      FASTDEPLOY_WHEEL_URL:
        description: "URL of the FastDeploy Wheel."
        required: true
        type: string
      CACHE_DIR:
        description: "Cache Dir Use"
        required: false
        type: string
        default: ""
      MODEL_CACHE_DIR:
        description: "Cache Dir Use"
        required: false
        type: string
        default: ""

jobs:
  run_tests_logprob:
    runs-on: [self-hosted, GPU-h20-1Cards]
    steps:
      - name: Code Prepare
        shell: bash
        env:
          docker_image: ${{ inputs.DOCKER_IMAGE }}
          paddletest_archive_url: ${{ inputs.PADDLETEST_ARCHIVE_URL }}
        run: |
            # Clean the repository directory before starting
            docker run --rm --net=host -v $(pwd):/workspace -w /workspace \
            -e "REPO_NAME=${REPO_NAME}" \
            -e "BASE_BRANCH=${BASE_BRANCH}" \
            ${docker_image} /bin/bash -c '
            rm -rf /workspace/*
            '
            wget -q ${paddletest_archive_url}
            tar -xf PaddleTest.tar.gz
            rm -rf PaddleTest.tar.gz
            cd PaddleTest
            git config --global user.name "FastDeployCI"
            git config --global user.email "<EMAIL>"
            git log -n 3 --oneline
      - name: logprob test
        shell: bash
        env:
          docker_image: ${{ inputs.DOCKER_IMAGE }}
          fastdeploy_wheel_url: ${{ inputs.FASTDEPLOY_WHEEL_URL }}
          CACHE_DIR: ${{ inputs.CACHE_DIR }}
          MODEL_CACHE_DIR: ${{ inputs.MODEL_CACHE_DIR }}
        run: |
          runner_name="${{ runner.name }}"
          last_char="${runner_name: -1}"

          if [[ "$last_char" =~ [0-7] ]]; then
            DEVICES="$last_char"
          else
            DEVICES="0"
          fi

          FLASK_PORT=$((9160 + DEVICES * 100))
          FD_API_PORT=$((9180 + DEVICES * 100))
          FD_ENGINE_QUEUE_PORT=$((9150 + DEVICES * 100))
          FD_METRICS_PORT=$((9170 + DEVICES * 100))

          CACHE_DIR="${CACHE_DIR:-$(dirname "$(dirname "${{ github.workspace }}")")}"
          echo "CACHE_DIR is set to ${CACHE_DIR}"
          if [ ! -f "${CACHE_DIR}/gitconfig" ]; then
            touch "${CACHE_DIR}/gitconfig"
          fi
          if [ ! -d "${MODEL_CACHE_DIR}" ]; then
            echo "Error: MODEL_CACHE_DIR '${MODEL_CACHE_DIR}' does not exist."
            exit 1
          fi

          PARENT_DIR=$(dirname "$WORKSPACE")

          docker run --ipc=host --pid=host --net=host \
          -v $(pwd):/workspace \
          -w /workspace \
          -e fastdeploy_wheel_url=${fastdeploy_wheel_url} \
          -e "FD_API_PORT=${FD_API_PORT}" \
          -e "FD_ENGINE_QUEUE_PORT=${FD_ENGINE_QUEUE_PORT}" \
          -e "FD_METRICS_PORT=${FD_METRICS_PORT}" \
          -e "FLASK_PORT=${FLASK_PORT}" \
          -v "${MODEL_CACHE_DIR}:/MODELDATA" \
          -v "${CACHE_DIR}/gitconfig:/etc/gitconfig:ro" \
          -v "${CACHE_DIR}/.cache:/root/.cache" \
          -v "${CACHE_DIR}/ConfigDir:/root/.config" \
          -e TZ="Asia/Shanghai" \
          --gpus '"device='"${DEVICES}"'"' ${docker_image} /bin/bash -c '
          # python -m pip install --pre paddlepaddle-gpu -i https://www.paddlepaddle.org.cn/packages/nightly/cu126/
          python -m pip install paddlepaddle-gpu==3.0.0.dev20250729 -i https://www.paddlepaddle.org.cn/packages/nightly/cu126/

          pip config set global.index-url http://pip.baidu.com/root/baidu/+simple/
          pip config set install.trusted-host  pip.baidu.com
          pip config set global.extra-index-url https://mirrors.tuna.tsinghua.edu.cn/pypi/web/simple
          python -m pip install ${fastdeploy_wheel_url}

          wget https://paddle-qa.bj.bcebos.com/zhengtianyu/tools/llm-deploy-linux-amd64
          chmod +x ./llm-deploy-linux-amd64
          ./llm-deploy-linux-amd64 -python python3.10 \
          -model_name ERNIE-4.5-0.3B-Paddle \
          -model_path /MODELDATA \
          --skip install

          cd PaddleTest/framework/ServeTest
          python3.10 deploy.py > dd.log 2>&1 &
          sleep 3
          curl -X POST http://0.0.0.0:${FLASK_PORT}/start \
              -H "Content-Type: application/json" \
              -d "{\"--model\": \"/MODELDATA/ERNIE-4.5-0.3B-Paddle\"}"

          curl -X POST http://localhost:${FLASK_PORT}/wait_for_infer?timeout=90
          set +e
          rm -rf ./baseline_output
          cp -r baseline/ERNIE-4.5-0.3B-Paddle ./baseline_output
          LOGPROB_EXIT_CODE=0
          python3.10 lanucher.py --request_template TOKEN_LOGPROB --url http://localhost:${FD_API_PORT}/v1/chat/completions  --case ./cases/demo.yaml  --concurrency 1 --name demo --exe logprob || LOGPROB_EXIT_CODE=$?
          echo "LOGPROB_EXIT_CODE=${LOGPROB_EXIT_CODE}" > /workspace/exit_code.env
          curl -X POST http://localhost:${FLASK_PORT}/stop
          sleep 10s
          cat *result.log
          exit 0
          '
          if [ $? -ne 0 ];then
            exit 1
          fi

          if [ -f exit_code.env ]; then
            cat exit_code.env >> $GITHUB_ENV
          fi
      - name: logprob test result
        if: ${{ env.LOGPROB_EXIT_CODE != 0 }}
        shell: bash
        run: |
          echo "logprob test failed with exit code ${{ env.LOGPROB_EXIT_CODE }}"
          exit 8
