name: PR Build and Test
on:
  pull_request:
    types: [opened, synchronize]
    branches: [develop, release/**]
permissions: read-all

concurrency:
  group: ${{ github.event.pull_request.number }}-${{ github.workflow }}
  cancel-in-progress: true

jobs:
  clone:
    name: FD-Clone-Linux
    uses: ./.github/workflows/_clone_linux.yml

  build:
    name: FD-Build-Linux
    needs: clone
    uses: ./.github/workflows/_build_linux.yml
    with:
      DOCKER_IMAGE: ccr-2vdh3abv-pub.cnc.bj.baidubce.com/paddlepaddle/paddleqa:cuda126-py310
      FASTDEPLOY_ARCHIVE_URL: ${{ needs.clone.outputs.repo_archive_url }}
      COMPILE_ARCH: "90"
      WITH_NIGHTLY_BUILD: "OFF"
      FD_VERSION: "0.0.0"

  resultshow:
    name: Use Build Output
    needs: build
    runs-on: ubuntu-latest
    steps:
      - name: Print wheel path
        run: |
          echo "The built wheel is located at: ${{ needs.build.outputs.wheel_path }}"

  unittest_coverage:
    name: Run FastDeploy Unit Tests and Coverage
    needs: [clone,build]
    uses: ./.github/workflows/_unit_test_coverage.yml
    with:
      DOCKER_IMAGE: ccr-2vdh3abv-pub.cnc.bj.baidubce.com/paddlepaddle/paddleqa:cuda126-py310
      FASTDEPLOY_ARCHIVE_URL: ${{ needs.clone.outputs.repo_archive_url }}
      FASTDEPLOY_WHEEL_URL: ${{ needs.build.outputs.wheel_path }}

  logprob_test:
    name: Run FastDeploy LogProb Tests
    needs: [build]
    uses: ./.github/workflows/_logprob_test_linux.yml
    with:
      DOCKER_IMAGE: ccr-2vdh3abv-pub.cnc.bj.baidubce.com/paddlepaddle/paddleqa:cuda126-py310
      PADDLETEST_ARCHIVE_URL: "https://xly-devops.bj.bcebos.com/PaddleTest/PaddleTest.tar.gz"
      FASTDEPLOY_WHEEL_URL: ${{ needs.build.outputs.wheel_path }}
      MODEL_CACHE_DIR: "/ssd2/actions-runner/ModelCache"
