name: CI

on:
  pull_request:
    branches:
      - develop
      - 'release/*'
  workflow_dispatch:

concurrency:
  group: ${{ github.event.pull_request.number }}
  cancel-in-progress: true

jobs:
  build:
    runs-on: [self-hosted, GPU-L20-4Card]
    steps:
      - name: Print current runner name
        run: |
          echo "Current runner name: ${{ runner.name }}"
      # Because the system version is lower than 2.23, the checkout cannot be used.
      # - name: Checkout code
      #   uses: actions/checkout@v4

      - name: Code Checkout
        env:
          docker_image: ccr-2vdh3abv-pub.cnc.bj.baidubce.com/paddlepaddle/paddle:fastdeploy-ciuse-cuda126
        run: |
          REPO="https://github.com/${{ github.repository }}.git"
          FULL_REPO="${{ github.repository }}"
          REPO_NAME="${FULL_REPO##*/}"
          BASE_BRANCH="${{ github.base_ref }}"
          # Clean the repository directory before starting
          docker run --rm --net=host -v $(pwd):/workspace -w /workspace \
          -e "REPO_NAME=${REPO_NAME}" \
          -e "BASE_BRANCH=${BASE_BRANCH}" \
          ${docker_image} /bin/bash -c '
            if [ -d ${REPO_NAME} ]; then
              echo "Directory ${REPO_NAME} exists, removing it..."
              rm -rf ${REPO_NAME}
            fi
          '
          git config --global user.name "FastDeployCI"
          git config --global user.email "<EMAIL>"
          git clone ${REPO} ${REPO_NAME} -b ${BASE_BRANCH}
          cd FastDeploy
          if [ "${{ github.event_name }}" = "pull_request" ]; then
            git fetch origin pull/${{ github.event.pull_request.number }}/head:pr/${{ github.event.pull_request.number }}
            git merge pr/${{ github.event.pull_request.number }}
            git log -n 3 --oneline
          else
            git checkout ${{ github.sha }}
            git log -n 3 --oneline
          fi

      - name: Run CI unittest
        env:
          docker_image: ccr-2vdh3abv-pub.cnc.bj.baidubce.com/paddlepaddle/paddle:fastdeploy-ciuse-cuda126
        run: |
          runner_name="${{ runner.name }}"
          last_char="${runner_name: -1}"

          if [ "${last_char}" = "1" ]; then
            gpu_id=2
            DEVICES="2,3"
          else
            gpu_id=0
            DEVICES="0,1"
          fi
          FD_API_PORT=$((9180 + gpu_id * 100))
          FD_ENGINE_QUEUE_PORT=$((9150 + gpu_id * 100))
          FD_METRICS_PORT=$((9170 + gpu_id * 100))

          PARENT_DIR=$(dirname "$WORKSPACE")
          echo "PARENT_DIR:$PARENT_DIR"
          docker run --rm --net=host -v $(pwd):/workspace -w /workspace \
          -v "/ssd4/GithubActions/gitconfig:/etc/gitconfig:ro" \
          -v "/ssd4/GithubActions/ModelData:/ModelData:ro" \
          -v "/ssd4/GithubActions/CacheDir:/root/.cache" \
          -v "/ssd4/GithubActions/ConfigDir:/root/.config" \
          -e "MODEL_PATH=/ModelData" \
          -e "FD_API_PORT=${FD_API_PORT}" \
          -e "FD_ENGINE_QUEUE_PORT=${FD_ENGINE_QUEUE_PORT}" \
          -e "FD_METRICS_PORT=${FD_METRICS_PORT}" \
          --gpus '"device='"${DEVICES}"'"' ${docker_image} /bin/bash -c "
          git config --global --add safe.directory /workspace/FastDeploy
          cd FastDeploy
          bash scripts/run_ci.sh
          "
