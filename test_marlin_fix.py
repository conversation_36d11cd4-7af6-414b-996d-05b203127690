#!/usr/bin/env python3
# Copyright (c) 2025 PaddlePaddle Authors. All Rights Reserved.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

"""
Test script to verify Marlin async memory copy fixes.
"""

import os
import sys
import subprocess
import paddle

def test_marlin_header_compilation():
    """Test compilation of Marlin header with async memory copy fixes."""
    print("🔨 Testing Marlin header compilation...")
    
    # Create a simple test file that includes the Marlin header
    test_content = '''
#include "custom_ops/gpu_ops/moe/moe_wna16_marlin_utils/marlin.cuh"

__global__ void test_kernel() {
    // Test the async memory copy functions
    void* smem_ptr = nullptr;
    const void* glob_ptr = nullptr;
    
    // These should work on both cc>=80 and cc<80
    MARLIN_NAMESPACE_NAME::cp_async4(smem_ptr, glob_ptr);
    MARLIN_NAMESPACE_NAME::cp_async_fence();
    MARLIN_NAMESPACE_NAME::cp_async_wait<0>();
}

int main() {
    return 0;
}
'''
    
    try:
        with open("test_marlin_async.cu", "w") as f:
            f.write(test_content)
        
        # Get compute capability
        cc = 70  # Default
        try:
            if paddle.is_compiled_with_cuda():
                device_id = paddle.get_device().split(':')[-1]
                prop = paddle.device.cuda.get_device_properties(int(device_id))
                cc = prop.major * 10 + prop.minor
        except:
            pass
        
        print(f"Testing with compute capability: {cc}")
        
        # Test compilation
        nvcc_cmd = [
            "nvcc", "-c", "test_marlin_async.cu",
            "-o", "test_marlin_async.o",
            "-std=c++17",
            f"-gencode=arch=compute_{cc},code=sm_{cc}",
            "-I.",
            "-DMOE_COMPATIBILITY_CC70=1" if cc < 80 else "",
        ]
        
        # Remove empty strings
        nvcc_cmd = [arg for arg in nvcc_cmd if arg]
        
        result = subprocess.run(nvcc_cmd, capture_output=True, text=True, cwd=".")
        
        # Clean up
        for temp_file in ["test_marlin_async.cu", "test_marlin_async.o"]:
            if os.path.exists(temp_file):
                os.remove(temp_file)
        
        if result.returncode == 0:
            print("✅ Marlin header compilation successful")
            return True
        else:
            print("❌ Marlin header compilation failed:")
            print("STDERR:", result.stderr)
            return False
            
    except Exception as e:
        print(f"❌ Test failed: {e}")
        return False

def test_gptq_marlin_repack_compilation():
    """Test compilation of the problematic gptq_marlin_repack.cu file."""
    print("\n🔨 Testing gptq_marlin_repack.cu compilation...")
    
    gptq_file = "custom_ops/gpu_ops/moe/gptq_marlin_repack.cu"
    if not os.path.exists(gptq_file):
        print(f"❌ File not found: {gptq_file}")
        return False
    
    try:
        # Get compute capability
        cc = 70  # Default
        try:
            if paddle.is_compiled_with_cuda():
                device_id = paddle.get_device().split(':')[-1]
                prop = paddle.device.cuda.get_device_properties(int(device_id))
                cc = prop.major * 10 + prop.minor
        except:
            pass
        
        print(f"Testing with compute capability: {cc}")
        
        # Test compilation
        nvcc_cmd = [
            "nvcc", "-c", gptq_file,
            "-o", "test_gptq_marlin.o",
            "-std=c++17",
            f"-gencode=arch=compute_{cc},code=sm_{cc}",
            "-Icustom_ops/gpu_ops",
            "-Icustom_ops/gpu_ops/moe",
            "-DMOE_COMPATIBILITY_CC70=1" if cc < 80 else "",
        ]
        
        # Remove empty strings
        nvcc_cmd = [arg for arg in nvcc_cmd if arg]
        
        result = subprocess.run(nvcc_cmd, capture_output=True, text=True, cwd=".")
        
        # Clean up
        if os.path.exists("test_gptq_marlin.o"):
            os.remove("test_gptq_marlin.o")
        
        if result.returncode == 0:
            print("✅ gptq_marlin_repack.cu compilation successful")
            return True
        else:
            print("❌ gptq_marlin_repack.cu compilation failed:")
            print("STDERR:", result.stderr)
            return False
            
    except Exception as e:
        print(f"❌ Test failed: {e}")
        return False

def check_marlin_header_fix():
    """Check if the Marlin header fix is in place."""
    print("\n🔍 Checking Marlin header fix...")
    
    marlin_header = "custom_ops/gpu_ops/moe/moe_wna16_marlin_utils/marlin.cuh"
    if not os.path.exists(marlin_header):
        print(f"❌ Marlin header not found: {marlin_header}")
        return False
    
    try:
        with open(marlin_header, 'r') as f:
            content = f.read()
        
        # Check for the fix
        if "__device__ inline void cp_async4(" in content:
            print("✅ Found __device__ inline cp_async4 function")
        else:
            print("❌ Missing __device__ inline cp_async4 function")
            return False
        
        if "__device__ inline void cp_async_fence(" in content:
            print("✅ Found __device__ inline cp_async_fence function")
        else:
            print("❌ Missing __device__ inline cp_async_fence function")
            return False
        
        if "__device__ inline void cp_async_wait(" in content:
            print("✅ Found __device__ inline cp_async_wait function")
        else:
            print("❌ Missing __device__ inline cp_async_wait function")
            return False
        
        # Check for fallback implementations
        if "memcpy(smem_ptr, glob_ptr, 16)" in content:
            print("✅ Found memcpy fallback implementation")
        else:
            print("❌ Missing memcpy fallback implementation")
            return False
        
        print("✅ All Marlin header fixes are in place")
        return True
        
    except Exception as e:
        print(f"❌ Failed to check Marlin header: {e}")
        return False

def main():
    """Run all tests."""
    print("Marlin Async Memory Copy Fix Test")
    print("=" * 40)
    
    if not paddle.is_compiled_with_cuda():
        print("❌ CUDA not available, skipping tests")
        return False
    
    tests = [
        ("Marlin Header Fix Check", check_marlin_header_fix),
        ("Marlin Header Compilation", test_marlin_header_compilation),
        ("GPTQ Marlin Repack Compilation", test_gptq_marlin_repack_compilation),
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            print(f"\n🧪 Running {test_name} test...")
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ Test '{test_name}' crashed: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 40)
    print("Test Summary:")
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"  {status}: {test_name}")
        if result:
            passed += 1
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        print("\n🎉 All tests passed! Marlin async memory copy fix is working.")
        print("\n💡 Next steps:")
        print("   1. cd custom_ops")
        print("   2. python setup_ops.py build_ext --inplace")
        return True
    else:
        print("\n⚠️  Some tests failed. Please check the fixes.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
